let counter = 0;

import FeexVeb from "../lib/feexveb.js";
const CONTENT_TYPES = {
  ".html": "text/html",
  ".js": "text/javascript",
  ".css": "text/css",
  ".json": "application/json",
  ".png": "image/png",
  ".jpg": "image/jpeg",
  ".gif": "image/gif",
  ".svg": "image/svg+xml",
  ".ico": "image/x-icon",
};

function getContentType(path: string): string {
  for (const [ext, type] of Object.entries(CONTENT_TYPES)) {
    if (path.endsWith(ext)) {
      return type;
    }
  }
  return "text/plain";
}

async function serveFile(path: string): Promise<Response> {
  try {
    path = path.replace(/^\//, "");
    if (path === "" || path === "/") {
      path = "index.html";
    }
    const file = await Deno.open(path, { read: true });
    const contentType = getContentType(path);

    return new Response(file.readable, {
      headers: {
        "content-type": contentType,
      },
    });
  } catch (e) {
    console.error(`Error serving file: ${path}`, e);
    return new Response("Not Found", { status: 404 });
  }
}

/**
 * Main request handler
 */
async function requestHandler(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const path = url.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Check if it's an HTMX request
  const isHtmx = req.headers.get("HX-Request") === "true";

  if (path.startsWith("/api/")) {
    if (path === "/api/counter/value") {
      const className = counter % 2 === 0 ? "counter-value even" : "counter-value odd";

      if (isHtmx) {
        return new Response(
          `<div class="${className}">${counter}</div>`,
          {
            headers: { "Content-Type": "text/html" }
          }
        );
      } else {
        return new Response(
          JSON.stringify({ value: counter }),
          {
            headers: { "Content-Type": "application/json" }
          }
        );
      }
    } else if (path === "/api/counter/increment" && method === "POST") {
      // Increment counter
      counter++;

      const className = counter % 2 === 0 ? "counter-value even" : "counter-value odd";

      if (isHtmx) {
        return new Response(
          `<div class="${className}">${counter}</div>`,
          {
            headers: { "Content-Type": "text/html" }
          }
        );
      } else {
        return new Response(
          JSON.stringify({ value: counter }),
          {
            headers: { "Content-Type": "application/json" }
          }
        );
      }
    } else if (path === "/api/counter/decrement" && method === "POST") {
      counter--;

      const className = counter % 2 === 0 ? "counter-value even" : "counter-value odd";

      if (isHtmx) {
        return new Response(
          `<div class="${className}">${counter}</div>`,
          {
            headers: { "Content-Type": "text/html" }
          }
        );
      } else {
        return new Response(
          JSON.stringify({ value: counter }),
          {
            headers: { "Content-Type": "application/json" }
          }
        );
      }
    } else if (path === "/api/counter/reset" && method === "POST") {
      counter = 0;

      const className = counter % 2 === 0 ? "counter-value even" : "counter-value odd";

      if (isHtmx) {
        return new Response(
          `<div class="${className}">${counter}</div>`,
          {
            headers: { "Content-Type": "text/html" }
          }
        );
      } else {
        return new Response(
          JSON.stringify({ value: counter }),
          {
            headers: { "Content-Type": "application/json" }
          }
        );
      }
    } else if (path === "/api/counter/oob" && method === "POST") {
      counter++;

      const className = counter % 2 === 0 ? "counter-value even" : "counter-value odd";

      if (isHtmx) {
        return new Response(
          `<div id="counter-value" hx-swap-oob="true" class="${className}">${counter}</div>
           <div id="realtime-counter" hx-swap-oob="true" class="${className}">${counter}</div>
           <span id="counter-oob" hx-swap-oob="true">${counter}</span>
           <div>Counter updated to: ${counter}</div>`,
          {
            headers: { "Content-Type": "text/html" }
          }
        );
      } else {
        return new Response(
          JSON.stringify({ value: counter }),
          {
            headers: { "Content-Type": "application/json" }
          }
        );
      }
    }

    return new Response("API Endpoint Not Found", { status: 404 });
  }
  if (path === "/" || path === "/index.html") {
    return new Response(
      `<!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>FeexVeb Examples - Server & Client Demos</title>
        <script src="https://unpkg.com/htmx.org@2.0.4" integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+" crossorigin="anonymous"></script>
        <style>
          ${FeexVeb.styling.monospaceCssForHtml}
          .demo-links {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
          }
          .demo-link {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 1rem 2rem;
            margin: 0.5rem;
            border-radius: 4px;
            font-weight: bold;
            transition: background-color 0.2s;
          }
          .demo-link:hover {
            background: #0056b3;
            color: white;
          }
          .demo-link.secondary {
            background: #6c757d;
          }
          .demo-link.secondary:hover {
            background: #545b62;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>FeexVeb Examples</h1>
          <p>Welcome to FeexVeb! Choose your demo experience:</p>

          <div class="demo-links">
            <h2>Available Demos</h2>
            <a href="/examples/counter/" class="demo-link">
              📱 Client-Side Demo
              <br><small>FeexVeb Simplified API with Web Components</small>
            </a>
            <a href="/htmx-demo" class="demo-link secondary">
              🌐 Server-Side Demo
              <br><small>HTMX Integration with Server-Driven Updates</small>
            </a>
          </div>

          <div class="counter-component">
            <h2 class="counter-title">Quick HTMX Preview</h2>
            <p>Here's a quick preview of server-driven HTMX functionality:</p>

            <div id="counter-value" hx-get="/api/counter/value" hx-trigger="load">
              Loading...
            </div>

            <div class="counter-controls">
              <button
                class="counter-btn decrement"
                hx-post="/api/counter/decrement"
                hx-target="#counter-value"
              >
                Decrement
              </button>

              <button
                class="counter-btn"
                hx-post="/api/counter/increment"
                hx-target="#counter-value"
              >
                Increment
              </button>

              <button
                class="counter-btn reset"
                hx-post="/api/counter/reset"
                hx-target="#counter-value"
              >
                Reset
              </button>
            </div>

            <p><small>👆 This counter updates via server API calls.
            <a href="/htmx-demo">See the full HTMX demo</a> for more examples.</small></p>
          </div>

          <div class="counter-component">
            <h2 class="counter-title">About FeexVeb</h2>
            <p>FeexVeb is a minimal library for building web applications with:</p>
            <ul>
              <li><strong>Simplified API:</strong> Reduced boilerplate for faster development</li>
              <li><strong>Reactive State:</strong> Powered by Maverick.js Signals</li>
              <li><strong>HTMX Integration:</strong> Seamless server-side interactions</li>
              <li><strong>Monospace Design:</strong> Beautiful default styling following "The Monospace Web" principles</li>
              <li><strong>Web Components:</strong> Standards-based custom elements</li>
            </ul>
            <p>Choose a demo above to explore different aspects of FeexVeb!</p>
          </div>
        </div>
      </body>
      </html>`,
      {
        headers: { "Content-Type": "text/html" }
      }
    );
  }

  // Full HTMX demo route
  if (path === "/htmx-demo") {
    return new Response(
      `<!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>FeexVeb HTMX Demo - Server-Driven Updates</title>
        <script src="https://unpkg.com/htmx.org@2.0.4" integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+" crossorigin="anonymous"></script>
        <style>
          ${FeexVeb.styling.monospaceCssForHtml}
        </style>
      </head>
      <body>
        <div class="container">
          <h1>FeexVeb HTMX Demo</h1>
          <p>This demo showcases server-driven updates using HTMX with FeexVeb's monospace design system.</p>

          <nav style="margin: 2rem 0; text-align: center;">
            <a href="/" style="color: #007bff; text-decoration: none; margin-right: 1rem;">← Back to Home</a>
            <a href="/examples/counter/" style="color: #007bff; text-decoration: none;">View Client-Side Demo →</a>
          </nav>

          <div class="counter-component">
            <h2 class="counter-title">Server-side Counter</h2>
            <p>All state changes are handled by the server. Click buttons to see HTMX in action.</p>

            <div id="counter-value" hx-get="/api/counter/value" hx-trigger="load">
              Loading...
            </div>

            <div class="counter-controls">
              <button
                class="counter-btn decrement"
                hx-post="/api/counter/decrement"
                hx-target="#counter-value"
              >
                Decrement
              </button>

              <button
                class="counter-btn"
                hx-post="/api/counter/increment"
                hx-target="#counter-value"
              >
                Increment
              </button>

              <button
                class="counter-btn reset"
                hx-post="/api/counter/reset"
                hx-target="#counter-value"
              >
                Reset
              </button>
            </div>
          </div>

          <div class="counter-component">
            <h2 class="counter-title">Real-time Counter</h2>
            <p>Updates every 2 seconds from the server automatically.</p>

            <div
              id="realtime-counter"
              hx-get="/api/counter/value"
              hx-trigger="load, every 2s"
            >
              Loading...
            </div>
          </div>

          <div class="counter-component">
            <h2 class="counter-title">Out-of-band Counter Update</h2>
            <p>Demonstrates how to update multiple elements at once with a single request.</p>

            <div id="counter-display">
              Current value: <span id="counter-oob">Loading...</span>
            </div>

            <div class="counter-controls">
              <button
                class="counter-btn"
                hx-post="/api/counter/oob"
                hx-target="#counter-display"
              >
                Update All Counters
              </button>
            </div>
          </div>

          <div class="counter-component">
            <h2 class="counter-title">HTMX Features Demonstrated</h2>
            <ul>
              <li><strong>hx-get:</strong> Load content from server on page load</li>
              <li><strong>hx-post:</strong> Send POST requests to server endpoints</li>
              <li><strong>hx-target:</strong> Specify which element to update</li>
              <li><strong>hx-trigger:</strong> Control when requests are made (load, every 2s, click)</li>
              <li><strong>Server responses:</strong> Return HTML fragments that replace target elements</li>
              <li><strong>Out-of-band updates:</strong> Update multiple elements with one request</li>
            </ul>
            <p>All styling follows "The Monospace Web" principles for clean, readable design.</p>
          </div>
        </div>
      </body>
      </html>`,
      {
        headers: { "Content-Type": "text/html" }
      }
    );
  }

  // Handle examples directory
  if (path.startsWith("/examples/")) {
    let filePath = `.${path}`;

    // If path ends with /, try to serve index.html
    if (path.endsWith("/")) {
      filePath = `${filePath}index.html`;
    }

    try {
      return await serveFile(filePath);
    } catch {
      // If index.html doesn't work, try the original path
      try {
        return await serveFile(`.${path}`);
      } catch {
        return new Response("File not found", { status: 404 });
      }
    }
  }

  return await serveFile(path);
}

const port = 8000;
console.log(`HTTP server running at http://localhost:${port}/`);

Deno.serve({ port }, requestHandler);