<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FeexVeb Client-Side Demo - Simplified API</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"
        integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+"
        crossorigin="anonymous"></script>
    <style>
        body {
            font-family: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Consolas, "Liberation Mono", monospace;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
            background: #fafafa;
        }

        .counter-component {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .counter-value {
            font-size: 2rem;
            font-weight: bold;
            text-align: center;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
            min-height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .counter-value.even {
            background: #e8f5e8;
            color: #2d5a2d;
        }

        .counter-value.odd {
            background: #fff3cd;
            color: #856404;
        }

        .counter-value.server {
            background: #d4edda;
            color: #155724;
        }

        .counter-value.client {
            background: #cce5ff;
            color: #004085;
        }

        .counter-value.loading {
            opacity: 0.6;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 0.6;
            }

            50% {
                opacity: 1;
            }
        }

        .counter-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            margin: 0.25rem;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 1rem;
            transition: background-color 0.2s;
        }

        .counter-btn:hover:not(:disabled) {
            background: #0056b3;
        }

        .counter-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .counter-btn.decrement {
            background: #dc3545;
        }

        .counter-btn.decrement:hover:not(:disabled) {
            background: #c82333;
        }

        .counter-btn.reset {
            background: #6c757d;
        }

        .counter-btn.reset:hover:not(:disabled) {
            background: #545b62;
        }

        .counter-controls {
            text-align: center;
            margin: 1rem 0;
        }

        .hybrid-counter {
            border: 2px solid #007bff;
        }

        .counter-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .server-section,
        .client-section {
            text-align: center;
        }

        .status {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.5rem;
        }

        .auto-sync {
            text-align: center;
            margin: 1rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .sync-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
        }

        .sync-btn:hover {
            background: #218838;
        }

        .explanation {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            margin-top: 1rem;
        }

        .explanation ul {
            margin: 0.5rem 0;
            padding-left: 1.5rem;
        }

        .explanation li {
            margin: 0.25rem 0;
        }

        .demo-notes {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 1rem;
            margin: 2rem 0;
        }

        .demo-notes h3 {
            margin-top: 0;
            color: #856404;
        }

        .demo-notes code {
            background: #f8f9fa;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: inherit;
        }

        h1,
        h2,
        h3 {
            color: #333;
        }

        h1 {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 1rem;
        }

        h2 {
            color: #007bff;
            margin-top: 2rem;
        }
    </style>
</head>

<body>
    <nav
        style="background: #f8f9fa; padding: 1rem; margin-bottom: 2rem; text-align: center; border-bottom: 1px solid #dee2e6;">
        <a href="/" style="color: #007bff; text-decoration: none; margin-right: 1rem;">← Back to Home</a>
        <a href="/htmx-demo" style="color: #007bff; text-decoration: none;">View Server-Side Demo →</a>
    </nav>

    <div id="app">
        <h1>Loading FeexVeb Client-Side Demo...</h1>
        <p>Please wait while the FeexVeb components initialize...</p>
        <p><em>This demo showcases FeexVeb's simplified API for building reactive Web Components.</em></p>
    </div>

    <!-- Load FeexVeb and the simplified counter examples -->
    <script type="module" src="./counter.js"></script>
</body>

</html>