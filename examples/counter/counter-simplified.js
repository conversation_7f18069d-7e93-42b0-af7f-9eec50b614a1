/**
 * Simplified Counter Example - Demonstrating the new streamlined FeexVeb API
 *
 * This example shows how the simplified API reduces boilerplate while maintaining
 * all the functionality of the original counter example.
 */
import FeexVeb from "../../lib/feexveb.js";

// BEFORE: Original verbose API
/*
FeexVeb.defineComponent({
  tag: 'fx-counter-old',
  shadowMode: 'open',
  useMonospaceStyles: true,

  setup: (ctx) => {
    const element = ctx.element;
    const initialCount = FeexVeb.numAttr(element, 'initial-count', 0);
    const count = FeexVeb.useState(initialCount);
    const isEven = FeexVeb.useComputed(() => count.get() % 2 === 0, [count]);

    return {
      state: { count, isEven },
      methods: {
        increment: () => count.set(count.get() + 1),
        decrement: () => count.set(count.get() - 1),
        reset: () => count.set(initialCount)
      }
    };
  },

  render: (ctx) => {
    const title = FeexVeb.attr(ctx.element, 'title', 'Counter');
    const valueClass = ctx.isEven.get() ? 'counter-value even' : 'counter-value odd';

    return (
      <div class="container">
        <h2>{title}</h2>
        <div class={valueClass}>{ctx.count.get()}</div>
        <button onclick={ctx.increment}>Increment</button>
        <button onclick={ctx.decrement}>Decrement</button>
        <button onclick={ctx.reset}>Reset</button>
      </div>
    );
  },

  attributes: ['title', 'initial-count']
});
*/

// AFTER: New simplified API
FeexVeb.component({
  tag: 'fx-counter-simple',

  // Declarative state definition
  state: {
    count: 0  // Initial value, will be overridden by 'initial-count' attribute
  },

  // Declarative computed state with direct state access
  computed: {
    isEven: (state) => state.count % 2 === 0
  },

  // Declarative methods with direct state access
  methods: {
    increment: (state) => state.count++,
    decrement: (state) => state.count--,
    reset: (state) => state.count = 0  // Will be overridden by initial-count logic
  },

  // Declarative attributes with automatic type inference and defaults
  attrs: {
    'title': { type: 'string', default: 'Counter' },
    'initial-count': { type: 'number', default: 0 }
  },

  // Simplified render function with direct state access
  render: ({ count, isEven, increment, decrement, reset, title }) => {
    const valueClass = isEven ? 'counter-value even' : 'counter-value odd';

    return (
      <div class="container">
        <h2>{title}</h2>
        <div class={valueClass}>{count}</div>
        <div>
          <button class="counter-btn" onclick={decrement}>Decrement</button>
          <button class="counter-btn" onclick={increment}>Increment</button>
          <button class="counter-btn" onclick={reset}>Reset</button>
        </div>
      </div>
    );
  }
});

// Even simpler for basic components
FeexVeb.component({
  tag: 'fx-simple-counter',

  state: {
    count: 0
  },

  methods: {
    increment: (state) => state.count++
  },

  render: ({ count, increment }) => (
    <div>
      <span>Count: {count}</span>
      <button onclick={increment}>+</button>
    </div>
  )
});

// Advanced usage: mixing simplified API with custom setup for complex cases
FeexVeb.component({
  tag: 'fx-hybrid-counter',

  state: {
    count: 0
  },

  computed: {
    doubled: (state) => state.count * 2
  },

  methods: {
    increment: (state) => state.count++
  },

  // Custom setup for advanced features (event bus, effects, etc.)
  setup: (ctx) => {
    // Subscribe to global reset event (using global EventBus)
    const resetListener = () => {
      ctx.states.count.set(0);
    };

    // Simple event listener instead of EventBus for demo
    document.addEventListener('reset-all-counters', resetListener);

    return {
      effects: [
        () => document.removeEventListener('reset-all-counters', resetListener)
      ]
    };
  },

  render: ({ count, doubled, increment }) => (
    <div>
      <div>Count: {count}</div>
      <div>Doubled: {doubled}</div>
      <button onclick={increment}>Increment</button>
    </div>
  )
});

// Comparison: Lines of code reduction
console.log(`
API Comparison:
- Original API: ~45 lines for basic counter
- Simplified API: ~25 lines for same functionality
- Reduction: ~44% less boilerplate code

Benefits:
✓ Direct state access (no .get() calls)
✓ Declarative state/computed/methods definition
✓ Automatic attribute handling with type inference
✓ Smart defaults (shadowMode: 'open', useMonospaceStyles: true)
✓ Backward compatibility (defineComponent still available)
✓ Progressive enhancement (can mix with custom setup)
`);

// Initialize the demo
const init = () => {
  const root = document.getElementById('app') || document.body;

  FeexVeb.render(root, (
    <div>
      <h1>Simplified FeexVeb API Demo</h1>

      <h2>Full-featured Counter</h2>
      <fx-counter-simple title="Simplified Counter" initial-count="5"></fx-counter-simple>

      <h2>Minimal Counter</h2>
      <fx-simple-counter></fx-simple-counter>

      <h2>Hybrid Counter (with custom setup)</h2>
      <fx-hybrid-counter></fx-hybrid-counter>
    </div>
  ));
};

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}
