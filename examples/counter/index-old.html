<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FeexVeb Counter Example</title>
  <!-- Import HTMX for server-side interactions -->
  <script src="https://unpkg.com/htmx.org@2.0.4" integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+" crossorigin="anonymous"></script>
  <!-- Import the counter component -->
  <script type="module" src="./counter.js"></script>
</head>
<body>
  <div id="app">
    <!-- The app will be rendered here by FeexVeb -->
    <div>Loading FeexVeb Counter example...</div>
  </div>
</body>
</html>